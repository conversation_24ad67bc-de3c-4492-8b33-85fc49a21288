<template>
  <div class="register-container">
    <el-form
      autoComplete="on"
      :model="registerForm"
      :rules="registerRules"
      ref="registerForm"
      label-position="left"
      label-width="0px"
      class="card-box register-form"
    >
      <h3 class="title">用户注册</h3>
      <el-form-item prop="name">
        <span class="svg-container svg-container_register">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          v-model="registerForm.name"
          autoComplete="on"
          placeholder="请输入用户名"
        />
      </el-form-item>
      <el-form-item prop="userType" class="user-type-form-item">
        <el-radio-group
          v-model="registerForm.userType"
          class="user-type-radio-group"
        >
          <el-radio :label="0">个人</el-radio>
          <el-radio :label="1">企业</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="password">
        <span class="svg-container">
          <svg-icon icon-class="password"></svg-icon>
        </span>
        <el-input
          type="password"
          v-model="registerForm.password"
          autoComplete="on"
          placeholder="请输入密码"
        ></el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <span class="svg-container">
          <svg-icon icon-class="password"></svg-icon>
        </span>
        <el-input
          type="password"
          v-model="registerForm.confirmPassword"
          autoComplete="on"
          placeholder="请确认密码"
        ></el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="registerForm.userType === 0">
        <span class="svg-container">
          <svg-icon icon-class="code"></svg-icon>
        </span>
        <el-input
          v-model="registerForm.code"
          autoComplete="on"
          placeholder="请输入邀请码"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          style="width:100%;"
          :loading="loading"
          @click.native.prevent="handleRegister"
        >
          注册
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="text" style="width:100%;" @click="goToLogin">
          已有账号？去登录
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: "register",
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error("密码长度不能少于6位"));
      } else {
        callback();
      }
    };
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    const validatecode = (rule, value, callback) => {
      if (this.registerForm.userType === 0 && !value) {
        callback(new Error("请输入邀请码"));
      } else {
        callback();
      }
    };
    return {
      registerForm: {
        name: "",
        userType: 0,
        password: "",
        confirmPassword: "",
        code: ""
      },
      registerRules: {
        name: [
          { required: true, trigger: "blur", message: "请输入用户名" },
          {
            min: 3,
            max: 20,
            message: "用户名长度在 3 到 20 个字符",
            trigger: "blur"
          }
        ],
        userType: [
          { required: true, trigger: "change", message: "请选择用户类型" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入密码" },
          { validator: validatePassword, trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, trigger: "blur", message: "请确认密码" },
          { validator: validateConfirmPassword, trigger: "blur" }
        ],
        code: [
          { validator: validatecode, trigger: "blur" }
        ]
      },
      loading: false
    };
  },
  methods: {
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.loading = true;
          // 这里需要调用注册API
          console.log('注册表单数据:', this.registerForm);
          this.$store
            .dispatch("Register", this.registerForm)
            .then(data => {
              this.loading = false;
              console.log('注册响应数据:', data);
              if (data.code === 100) {
                this.$message.success("注册成功！");
                this.$router.push({ path: "/login" });
              } else {
                this.$message.error(data.message || "注册失败");
              }
            })
            .catch(error => {
              this.loading = false;
              console.error('注册错误:', error);
              this.$message.error("注册失败，请稍后重试");
            });
        } else {
          return false;
        }
      });
    },
    goToLogin() {
      this.$router.push({ path: "/login" });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
@import "../../styles/mixin.scss";
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.register-container {
  @include relative;
  height: 100vh;
  background-color: $bg;
  input:-webkit-autofill {
    box-shadow: 0 0 0px 1000px #293444 inset !important;
    -webkit-text-fill-color: #fff !important;
  }
  input {
    background: transparent;
    border: 0px;
    -webkit-appearance: none;
    border-radius: 0px;
    padding: 12px 5px 12px 15px;
    color: $light_gray;
    height: 47px;
  }
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;
  }
  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;
  }
  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    &_register {
      font-size: 20px;
    }
  }
  .title {
    font-size: 26px;
    color: $light_gray;
    margin: 0px auto 40px auto;
    text-align: center;
    font-weight: bold;
  }
  .register-form {
    position: absolute;
    left: 0;
    right: 0;
    width: 400px;
    padding: 35px 35px 15px 35px;
    margin: 80px auto;
  }
  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
  }
  .thirdparty-button {
    position: absolute;
    right: 35px;
    bottom: 28px;
  }
  .user-type-form-item {
    height: 47px;
    display: flex;
    align-items: center;
    .el-form-item__content {
      line-height: 47px;
      height: 47px;
      display: flex;
      align-items: center;
    }
    .user-type-radio-group {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 15px;
      .el-radio {
        color: $light_gray;
        margin: 0 15px;
        height: auto;
        display: flex;
        align-items: center;
        .el-radio__label {
          color: $light_gray;
          font-size: 14px;
        }
        .el-radio__input.is-checked .el-radio__inner {
          border-color: #409eff;
          background: #409eff;
        }
        .el-radio__inner {
          border-color: rgba(255, 255, 255, 0.3);
          background: transparent;
        }
      }
    }
  }
}
</style>
