import Vue from 'vue'
import Router from 'vue-router'
// in development env not use Lazy Loading,because Lazy Loading too many pages will cause webpack hot update too slow.so only in production use Lazy Loading
/* layout */
import Layout from '../views/layout/Layout'

const _import = require('./_import_' + process.env.NODE_ENV)
Vue.use(Router)
export const constantRouterMap = [
  {path: '/login', component: _import('login/index'), hidden: true},
  {path: '/register', component: _import('register/index'), hidden: true},
  {path: '/404', component: _import('404'), hidden: true},
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    name: '首页',
    hidden: true,
    meta: {title: '首页'},
    children: [{
      path: 'dashboard', component: _import('dashboard/index')
    }]
  },

  {
    path: '/setpsd',
    component: Layout,
    redirect: '/setpsd/setpsd',
    name: '修改密码',
    meta: {title: '修改密码', icon: 'table'},
    children: [
      {
        path: 'setpsd',
        name: '修改密码',
        component: _import('setpsd/setpsd'),
        meta: {title: '修改密码', icon: 'example'},
        menu: 'setpsd'
      },
    ]
    
  }
]
export default new Router({
  // mode: 'history', //后端支持可开
  scrollBehavior: () => ({y: 0}),
  routes: constantRouterMap
})
